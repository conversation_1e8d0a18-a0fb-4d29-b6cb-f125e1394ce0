# Loading in required libraries
import pandas as pd
import seaborn as sns
import numpy as np
import matplotlib.pyplot as plt

# Loading the Nobel Prize dataset
df = pd.read_csv("data/raw/nobel.csv")
df.head()

df.shape

gender_count = df['sex'].value_counts()
print(gender_count)

sns.set_style("whitegrid")
g = sns.catplot(data=df, kind="count", x="sex", hue="sex")
g.set_axis_labels("Sex", "Count")
g.fig.suptitle("Count of Nobel Prize Winners by Gender")
plt.show()

top_gender = gender_count.idxmax()
print(top_gender)

country_count = df['birth_country'].value_counts()
print(country_count.head())

sns.set_style("whitegrid")
# Get top 10 birth countries for better visualization
top_countries = df['birth_country'].value_counts().head(10).index
df_top = df[df['birth_country'].isin(top_countries)]
g = sns.catplot(data=df_top, kind="count", x="birth_country", order=top_countries, hue="birth_country")
g.set_axis_labels("Birth Country", "Count")
g.fig.suptitle("Count of Nobel Prize Winners by Birth Country (Top 10)")
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

top_country = country_count.idxmax()
print(top_country)

# Create decade column
df['decade'] = (df['year'] // 10) * 10

# Calculate ratio of US-born winners by decade
decade_stats = df.groupby('decade').agg({
    'birth_country': ['count', lambda x: (x == 'United States of America').sum()]
}).round(4)

decade_stats.columns = ['total_winners', 'us_winners']
decade_stats['us_ratio'] = decade_stats['us_winners'] / decade_stats['total_winners']

max_decade_usa = int(decade_stats['us_ratio'].idxmax())
print(max_decade_usa)

decade_stats = decade_stats.sort_index()

g = sns.relplot(data=decade_stats, x=decade_stats.index, y='us_ratio', kind="line")
g.set_axis_labels("Decade", "US-born Winners Ratio")
g.fig.suptitle("US-born Winners Ratio by Decade", y=1.05)
plt.show()


# Calculate female proportion by decade and category
female_stats = df.groupby(['decade', 'category']).agg({
    'sex': ['count', lambda x: (x == 'Female').sum()]
}).round(4)

female_stats.columns = ['total_winners', 'female_winners']
female_stats['female_proportion'] = female_stats['female_winners'] / female_stats['total_winners']

# Filter out combinations with very few winners (less than 3) to avoid misleading ratios
female_stats_filtered = female_stats[female_stats['total_winners'] >= 3]

print("Top 10 decade-category combinations by female proportion:")
top_female = female_stats_filtered.sort_values('female_proportion', ascending=False).head(10)
print(top_female)

# Get the top combination
max_female_idx = female_stats_filtered['female_proportion'].idxmax()
max_decade = max_female_idx[0]
max_category = max_female_idx[1]

max_female_dict = {max_decade: max_category}
print(max_female_dict)

g = sns.catplot(data=top_female, x='decade', y='female_proportion', hue='category', kind="bar")
g.set_axis_labels("Decade", "Female Proportion")
g.fig.suptitle("Female Proportion by Decade and Category (Top 10 Combinations)", y=1.05)
plt.show()

# Filter for female winners and sort by year
female_winners = df[df['sex'] == 'Female'].sort_values('year')

# Get the first woman
first_woman = female_winners.iloc[0]
first_woman_name = first_woman['full_name']
first_woman_category = first_woman['category']

print(f"First woman Nobel Prize winner: {first_woman_name}")
print(f"Category: {first_woman_category}")
print(f"Year: {first_woman['year']}")

# Count prizes by full name
prize_counts = df['full_name'].value_counts()

# Filter for those with more than one prize
multiple_winners = prize_counts[prize_counts > 1]

print("Multiple Nobel Prize winners:")
for name, count in multiple_winners.items():
    print(f"{name}: {count} prizes")
    # Show details for each winner
    winner_details = df[df['full_name'] == name][['year', 'category', 'laureate_type']]
    print(winner_details)
    print()

# Create the list of repeat winners
repeat_list = multiple_winners.index.tolist()

print(f"\nAnswer: List of repeat winners:")
print(repeat_list)
print(f"\nTotal number of repeat winners: {len(repeat_list)}")

# Final Summary: All answers stored in variables as requested
print("=== FINAL ANSWERS ===")
print(f"1. Most common gender: {top_gender}")
print(f"1. Most common birth country: {top_country}")
print(f"2. Decade with highest US ratio: {max_decade_usa}")
print(f"3. Decade-category with highest female proportion: {max_female_dict}")
print(f"4. First woman Nobel Prize winner: {first_woman_name}")
print(f"4. First woman's category: {first_woman_category}")
print(f"5. Repeat winners list: {repeat_list}")

# Verify all variables are properly defined
print("\n=== Variable Types ===")
print(f"top_gender type: {type(top_gender)}")
print(f"top_country type: {type(top_country)}")
print(f"max_decade_usa type: {type(max_decade_usa)}")
print(f"max_female_dict type: {type(max_female_dict)}")
print(f"first_woman_name type: {type(first_woman_name)}")
print(f"first_woman_category type: {type(first_woman_category)}")
print(f"repeat_list type: {type(repeat_list)}")